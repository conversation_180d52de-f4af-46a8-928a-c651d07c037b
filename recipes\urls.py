from django.urls import path, re_path
from . import views

app_name = 'recipes'

urlpatterns = [
    # Make unified search the default landing page
    path('', views.unified_recipe_search, name='home'),

    # Discover page
    path('discover/', views.discover_page, name='discover'),
    path('discover/random/', views.discover_random_recipe, name='discover_random'),
    path('discover/category/<str:category>/', views.discover_category_recipes, name='discover_category'),
    path('discover/area/<str:area>/', views.discover_area_recipes, name='discover_area'),

    # Featured carousel endpoint
    path('api/featured-carousel/', views.featured_carousel_recipes, name='featured_carousel_recipes'),

    # Search endpoints
    path('search/', views.unified_recipe_search, name='unified_search'),
    path('search/ingredients/', views.search_by_ingredients, name='search_by_ingredients'),  # Keep for backward compatibility
    path('history/', views.search_history, name='search_history'),

    # API-based views
    path('api/search/', views.api_search, name='api_search'),
    path('api/recipe/<str:recipe_id>/<str:source>/', views.api_recipe_detail, name='api_recipe_detail'),
    path('api/recipe/<str:recipe_id>/', views.api_recipe_detail, name='api_recipe_detail_default'),
    # Special pattern for API recipes in the main detail view (format: 'source:id')
    re_path(r'^recipe/(?P<recipe_id>[^/]+:[^/]+)/$', views.recipe_detail, name='recipe_detail_api'),
]
