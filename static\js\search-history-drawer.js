// Sliding drawer UI for Search History on Home Icon
// Binds hover events to display recent searches from localStorage

(function() {
    document.addEventListener('DOMContentLoaded', () => {
        const homeIcon = document.querySelector('.sidebar-link .bi-house');
        if (!homeIcon) return;
        const link = homeIcon.closest('.sidebar-link');

        // Create drawer container
        const drawer = document.createElement('div');
        drawer.className = 'search-history-drawer';
        document.body.appendChild(drawer);

        // Open drawer on hover
        link.addEventListener('mouseenter', () => {
            const items = window.searchHistoryManager?.getRecentSearches() || [];
            if (items.length === 0) {
                drawer.innerHTML = '<div class="text-muted p-3">No recent searches</div>';
            } else {
                // Group items by formatted date
                const groups = items.reduce((acc, item) => {
                    const dateKey = new Date(item.date).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
                    if (!acc[dateKey]) acc[dateKey] = [];
                    acc[dateKey].push(item);
                    return acc;
                }, {});
                // Sort dates descending
                const sortedDates = Object.keys(groups).sort((a, b) => new Date(b) - new Date(a));
                let html = '';
                sortedDates.forEach(dateKey => {
                    html += `<div class="search-history-date-group"><h5>${dateKey}</h5><ul>`;
                    groups[dateKey].forEach(item => {
                        html += `<li class="search-history-item" data-query="${item.query}" data-type="${item.searchType}">${item.query}</li>`;
                    });
                    html += `</ul></div>`;
                });
                drawer.innerHTML = html;
            }
            drawer.classList.add('open');
        });

        // Close drawer when leaving it
        drawer.addEventListener('mouseleave', () => {
            drawer.classList.remove('open');
        });

        // Navigate on click
        drawer.addEventListener('click', (e) => {
            const el = e.target.closest('.search-history-item');
            if (!el) return;
            const query = el.dataset.query;
            const type = el.dataset.type;
            const url = new URL(link.href, window.location.origin);
            url.searchParams.set('search_query', query);
            url.searchParams.set('search_type', type);
            window.location.href = url.toString();
        });
    });
})();
