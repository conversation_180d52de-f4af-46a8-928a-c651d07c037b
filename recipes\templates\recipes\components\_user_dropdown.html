<!-- User Dropdown Component -->
<div class="user-dropdown-container" x-data="userDropdown()" @click.away="closeDropdown()">
    <!-- User Icon Button -->
    <button class="user-icon-btn" @click="toggleDropdown()" :class="{ 'active': isOpen }" title="User Menu">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
        </svg>
        <div class="user-dropdown-indicator" :class="{ 'rotated': isOpen }">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="6,9 12,15 18,9"/>
            </svg>
        </div>
    </button>

    <!-- Mobile Backdrop (only on mobile) -->
    <div class="mobile-drawer-backdrop" x-show="isOpen" x-transition.opacity @click="closeDropdown()"></div>

    <!-- Desktop Sidebar Panel -->
    <div class="user-dropdown-sidebar" x-show="isOpen" :class="{ 'open': isOpen }" x-transition:enter="sidebar-enter" x-transition:enter-start="sidebar-enter-start" x-transition:enter-end="sidebar-enter-end" x-transition:leave="sidebar-leave" x-transition:leave-start="sidebar-leave-start" x-transition:leave-end="sidebar-leave-end">

        <!-- User Stats Section -->
        <div class="user-stats-section">
            <h4 class="user-section-title">Your Activity</h4>
            <div class="user-stats-grid">
                <div class="user-stat-card">
                    <div class="user-stat-icon">🔍</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-total-searches-desktop">0</div>
                        <div class="user-stat-label">Total Searches</div>
                    </div>
                </div>
                <div class="user-stat-card">
                    <div class="user-stat-icon">📅</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-week-searches-desktop">0</div>
                        <div class="user-stat-label">This Week</div>
                    </div>
                </div>
                <div class="user-stat-card">
                    <div class="user-stat-icon">⭐</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-popular-type-desktop">-</div>
                        <div class="user-stat-label">Most Used</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Actions Section -->
        <div class="user-actions-section">
            <h4 class="user-section-title">Quick Access</h4>
            <div class="user-actions-grid">
                <a href="{% url 'recipes:search_history' %}" class="user-action-item">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                            <path d="M3 3v5h5"/>
                            <polyline points="12,7 12,12 16,16"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Search History</div>
                        <div class="user-action-subtitle">View all searches</div>
                    </div>
                </a>

                <a href="#" class="user-action-item" onclick="showComingSoon('Favourites')">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Favourites</div>
                        <div class="user-action-subtitle">Saved recipes</div>
                    </div>
                </a>

                <a href="#" class="user-action-item" onclick="showComingSoon('Bookmarks')">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Bookmarks</div>
                        <div class="user-action-subtitle">Quick access</div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Drawer Content -->
    <div class="user-dropdown-mobile" x-show="isOpen" :class="{ 'open': isOpen }" x-transition:enter="mobile-enter" x-transition:enter-start="mobile-enter-start" x-transition:enter-end="mobile-enter-end" x-transition:leave="mobile-leave" x-transition:leave-start="mobile-leave-start" x-transition:leave-end="mobile-leave-end">

        <!-- User Stats Section -->
        <div class="user-stats-section">
            <h4 class="user-section-title">Your Activity</h4>
            <div class="user-stats-grid">
                <div class="user-stat-card">
                    <div class="user-stat-icon">🔍</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-total-searches-mobile">0</div>
                        <div class="user-stat-label">Total Searches</div>
                    </div>
                </div>
                <div class="user-stat-card">
                    <div class="user-stat-icon">📅</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-week-searches-mobile">0</div>
                        <div class="user-stat-label">This Week</div>
                    </div>
                </div>
                <div class="user-stat-card">
                    <div class="user-stat-icon">⭐</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-popular-type-mobile">-</div>
                        <div class="user-stat-label">Most Used</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Actions Section -->
        <div class="user-actions-section">
            <h4 class="user-section-title">Quick Access</h4>
            <div class="user-actions-grid">
                <a href="{% url 'recipes:search_history' %}" class="user-action-item">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                            <path d="M3 3v5h5"/>
                            <polyline points="12,7 12,12 16,16"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Search History</div>
                        <div class="user-action-subtitle">View all searches</div>
                    </div>
                </a>

                <a href="#" class="user-action-item" onclick="showComingSoon('Favourites')">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Favourites</div>
                        <div class="user-action-subtitle">Saved recipes</div>
                    </div>
                </a>

                <a href="#" class="user-action-item" onclick="showComingSoon('Bookmarks')">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Bookmarks</div>
                        <div class="user-action-subtitle">Quick access</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Future User Profile Section (placeholder) -->
        <div class="user-profile-section" style="display: none;">
            <h4 class="user-section-title">Profile</h4>
            <div class="user-profile-placeholder">
                <p>User profile features will be added here</p>
            </div>
        </div>
    </div>
</div>

<style>
/* User Dropdown Styles */
.user-dropdown-container {
    position: relative;
    display: inline-block;
}

.user-icon-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.user-icon-btn:hover {
    border-color: #FF6B35;
    color: #FF6B35;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
}

.user-icon-btn.active {
    border-color: #FF6B35;
    color: #FF6B35;
    background: #fff3f0;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}

.user-dropdown-indicator {
    transition: transform 0.3s ease;
}

.user-dropdown-indicator.rotated {
    transform: rotate(180deg);
}

/* Desktop Sidebar Panel */
.user-dropdown-sidebar {
    position: fixed;
    top: 0;
    left: 70px;
    width: 280px;
    height: 100%;
    background: white;
    border-right: 1px solid #f0f0f0;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.user-dropdown-sidebar.open {
    transform: translateX(0);
}

/* Mobile Bottom Drawer */
.user-dropdown-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    transform: translateY(100%);
    background: white;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0 -20px 40px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    max-height: 80vh;
    overflow-y: auto;
    display: none;
}

.user-dropdown-mobile.open {
    transform: translateY(0);
}

/* Mobile Backdrop */
.mobile-drawer-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    display: none;
}

/* Desktop Sidebar Animations */
.sidebar-enter {
    transition: all 0.3s ease;
}

.sidebar-enter-start {
    opacity: 0;
    transform: translateX(-100%);
}

.sidebar-enter-end {
    opacity: 1;
    transform: translateX(0);
}

.sidebar-leave {
    transition: all 0.25s ease;
}

.sidebar-leave-start {
    opacity: 1;
    transform: translateX(0);
}

.sidebar-leave-end {
    opacity: 0;
    transform: translateX(-100%);
}

/* Mobile Drawer Animations */
.mobile-enter {
    transition: all 0.3s ease;
}

.mobile-enter-start {
    opacity: 0;
    transform: translateY(100%);
}

.mobile-enter-end {
    opacity: 1;
    transform: translateY(0);
}

.mobile-leave {
    transition: all 0.25s ease;
}

.mobile-leave-start {
    opacity: 1;
    transform: translateY(0);
}

.mobile-leave-end {
    opacity: 0;
    transform: translateY(100%);
}

/* User Stats Section */
.user-stats-section {
    padding: 20px;
    border-bottom: 1px solid #f3f4f6;
    background: linear-gradient(135deg, #fff3f0, #ffffff);
}

.user-section-title {
    color: #FF6B35;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.user-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.user-stat-card {
    background: white;
    border-radius: 12px;
    padding: 12px;
    text-align: center;
    border: 1px solid #ffe8e1;
    transition: all 0.2s ease;
}

.user-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.1);
}

.user-stat-icon {
    font-size: 1.2rem;
    margin-bottom: 6px;
}

.user-stat-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: #FF6B35;
    line-height: 1;
    margin-bottom: 2px;
}

.user-stat-label {
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
}

/* User Actions Section */
.user-actions-section {
    padding: 20px;
}

.user-actions-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.user-action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.user-action-item:hover {
    background: #fff3f0;
    border-color: #ffe8e1;
    color: inherit;
    text-decoration: none;
}

.user-action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: #f9fafb;
    border-radius: 8px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.user-action-item:hover .user-action-icon {
    background: #FF6B35;
    color: white;
}

.user-action-content {
    flex: 1;
}

.user-action-title {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-action-subtitle {
    color: #6b7280;
    font-size: 12px;
}

/* Desktop Styles (992px and up) */
@media (min-width: 993px) {
    .user-dropdown-sidebar {
        display: block;
    }

    .user-dropdown-mobile {
        display: none !important;
    }

    .mobile-drawer-backdrop {
        display: none !important;
    }
}

/* Mobile Styles (992px and below) */
@media (max-width: 992px) {
    .user-dropdown-sidebar {
        display: none !important;
    }

    .user-dropdown-mobile {
        display: block;
    }

    .mobile-drawer-backdrop {
        display: block;
    }

    .user-stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .user-stat-card {
        display: flex;
        align-items: center;
        gap: 12px;
        text-align: left;
        padding: 10px 12px;
    }

    .user-stat-icon {
        font-size: 1.5rem;
        margin-bottom: 0;
    }

    .user-stat-number {
        font-size: 1.1rem;
    }

    .user-stat-label {
        font-size: 0.7rem;
    }
}
</style>

<script>
function userDropdown() {
    return {
        isOpen: false,
        
        init() {
            this.loadUserStats();
        },
        
        toggleDropdown() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.loadUserStats();
            }
        },
        
        closeDropdown() {
            this.isOpen = false;
        },
        
        loadUserStats() {
            if (window.searchHistoryManager) {
                const stats = window.searchHistoryManager.getSearchStats();

                // Update desktop elements
                const desktopTotal = document.getElementById('user-total-searches-desktop');
                const desktopWeek = document.getElementById('user-week-searches-desktop');
                const desktopPopular = document.getElementById('user-popular-type-desktop');

                if (desktopTotal) desktopTotal.textContent = stats.totalSearches;
                if (desktopWeek) desktopWeek.textContent = stats.recentActivity.length;

                // Update mobile elements
                const mobileTotal = document.getElementById('user-total-searches-mobile');
                const mobileWeek = document.getElementById('user-week-searches-mobile');
                const mobilePopular = document.getElementById('user-popular-type-mobile');

                if (mobileTotal) mobileTotal.textContent = stats.totalSearches;
                if (mobileWeek) mobileWeek.textContent = stats.recentActivity.length;

                // Find most popular search type
                const searchTypes = stats.searchTypes;
                const mostPopular = Object.keys(searchTypes).reduce((a, b) =>
                    searchTypes[a] > searchTypes[b] ? a : b, 'ingredients'
                );
                const popularText = stats.totalSearches > 0 ? mostPopular : '-';

                if (desktopPopular) desktopPopular.textContent = popularText;
                if (mobilePopular) mobilePopular.textContent = popularText;
            }
        }
    }
}

function showComingSoon(feature) {
    alert(`${feature} feature coming soon! This will allow you to save and organize your favorite recipes.`);
}
</script>
