<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Mobile Navigation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #4CAF50;
            --accent-color: #C62828;
            --neutral-color: #FFF8E1;
            --primary-dark: #E55A2B;
            --secondary-dark: #3D8B3F;
            --text-on-primary: #FFFFFF;
            --text-on-secondary: #FFFFFF;
            --text-dark: #333333;
            --text-light: #666666;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            min-height: 200vh;
            overflow-x: hidden;
        }

        /* Top Bar */
        .topbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            z-index: 100;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s ease;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .topbar.hidden {
            transform: translateY(-100%);
            opacity: 0;
        }

        .logo {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-dark);
        }

        /* Main Content */
        .content {
            padding: 80px 20px 100px;
            max-width: 400px;
            margin: 0 auto;
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            margin-bottom: 15px;
            font-size: 24px;
            color: var(--primary-color);
        }

        .card p {
            line-height: 1.6;
            color: var(--text-dark);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            z-index: 100;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s ease;
            box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
        }

        .bottom-nav.hidden {
            transform: translateY(100%);
            opacity: 0;
        }

        .nav-container {
            position: relative;
            width: 100%;
            max-width: 380px;
            height: 60px;
            background: var(--neutral-color);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px 12px;
            border-radius: 20px;
            z-index: 2;
        }

        .nav-item.active {
            background: rgba(76, 175, 80, 0.1);
        }

        .nav-item .icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            transition: all 0.3s ease;
        }

        .nav-item .label {
            font-size: 10px;
            color: var(--text-light);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-item.active .label {
            color: var(--secondary-color);
        }

        /* New Button (Elevated) - Center Position */
        .nav-item.new {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
            transition: all 0.3s ease;
        }

        .nav-item.new:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 12px 30px rgba(255, 107, 53, 0.5);
        }

        .nav-item.new .icon {
            color: var(--text-on-primary);
            margin-bottom: 0;
        }

        .nav-item.new .label {
            color: var(--text-on-primary);
            font-size: 10px;
            margin-top: 2px;
        }

        /* Curved cutout effect */
        .nav-container::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 40px;
            background: var(--neutral-color);
            border-radius: 0 0 40px 40px;
            z-index: 1;
        }

        .nav-container::after {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 50px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0 0 50px 50px;
            z-index: 0;
        }

        /* Icons */
        .home-icon,
        .discover-icon,
        .history-icon,
        .profile-icon {
            stroke: var(--text-light);
            fill: none;
            stroke-width: 2;
        }

        .nav-item.active .home-icon,
        .nav-item.active .discover-icon,
        .nav-item.active .history-icon,
        .nav-item.active .profile-icon {
            stroke: var(--secondary-color);
        }

        .plus-icon {
            stroke: var(--text-on-primary);
            fill: none;
            stroke-width: 2;
        }

        /* Responsive */
        @media (max-width: 375px) {
            .nav-container {
                max-width: 350px;
            }
            
            .nav-item {
                padding: 6px 8px;
            }
            
            .nav-item .label {
                font-size: 9px;
            }
        }

        /* Smooth scroll behavior enhancement */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <!-- Top Bar -->
    <div class="topbar" id="topbar">
        <div class="logo">App Name</div>
    </div>

    <!-- Main Content -->
    <div class="content">
        <div class="card">
            <h2>Welcome Home</h2>
            <p>This is your home screen. Scroll down to see the navigation hide and show with smooth animations.</p>
        </div>
        
        <div class="card">
            <h2>Your History</h2>
            <p>Access your recent activities and track your progress. The history tab keeps users engaged by showing their journey.</p>
        </div>
        
        <div class="card">
            <h2>Discover New Content</h2>
            <p>Explore and discover amazing new features and content tailored just for you.</p>
        </div>
        
        <div class="card">
            <h2>Create Something New</h2>
            <p>Use the elevated '+' button to create new content, posts, or start new projects.</p>
        </div>
        
        <div class="card">
            <h2>Your Profile</h2>
            <p>Manage your account, settings, and personal information. Profile access drives retention through personalization.</p>
        </div>
        
        <div class="card">
            <h2>Smooth Interactions</h2>
            <p>Notice how the navigation responds to your scrolling with smooth, aesthetic animations.</p>
        </div>
        
        <div class="card">
            <h2>Modern Design</h2>
            <p>Clean, modern interface with glassmorphism effects and thoughtful interactions.</p>
        </div>
        
        <div class="card">
            <h2>Keep Scrolling</h2>
            <p>Continue scrolling to test the improved hide/show behavior of both the top bar and bottom navigation.</p>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav" id="bottomNav">
        <div class="nav-container">
            <!-- Home - First position (left) for primary navigation -->
            <div class="nav-item active" data-page="home">
                <svg class="icon home-icon" viewBox="0 0 24 24">
                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                <span class="label">Home</span>
            </div>
            
            <!-- History - Second position (encourages return visits) -->
            <div class="nav-item" data-page="history">
                <svg class="icon history-icon" viewBox="0 0 24 24">
                    <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                    <path d="M3 3v5h5"></path>
                    <path d="M12 7v5l4 2"></path>
                </svg>
                <span class="label">History</span>
            </div>
            
            <!-- New - Center position (elevated for maximum visibility) -->
            <div class="nav-item new" data-page="new">
                <svg class="icon plus-icon" viewBox="0 0 24 24">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span class="label">New</span>
            </div>
            
            <!-- Discover - Fourth position (exploration after engagement) -->
            <div class="nav-item" data-page="discover">
                <svg class="icon discover-icon" viewBox="0 0 24 24">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
                <span class="label">Discover</span>
            </div>
            
            <!-- Profile - Last position (personalization and settings) -->
            <div class="nav-item" data-page="profile">
                <svg class="icon profile-icon" viewBox="0 0 24 24">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span class="label">Profile</span>
            </div>
        </div>
    </div>

    <script>
        let lastScrollTop = 0;
        let scrollTimeout;
        const topbar = document.getElementById('topbar');
        const bottomNav = document.getElementById('bottomNav');
        const navItems = document.querySelectorAll('.nav-item');
        
        // Enhanced scroll behavior for aesthetic hiding/showing
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Clear existing timeout
            clearTimeout(scrollTimeout);
            
            if (scrollTop > lastScrollTop && scrollTop > 60) {
                // Scrolling down - hide with smooth delay
                scrollTimeout = setTimeout(() => {
                    topbar.classList.add('hidden');
                    bottomNav.classList.add('hidden');
                }, 100);
            } else if (scrollTop < lastScrollTop) {
                // Scrolling up - show immediately
                topbar.classList.remove('hidden');
                bottomNav.classList.remove('hidden');
            }
            
            // Always show when at top
            if (scrollTop <= 0) {
                topbar.classList.remove('hidden');
                bottomNav.classList.remove('hidden');
            }
            
            lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
        });
        
        // Navigation item click handlers
        navItems.forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items (except new button)
                navItems.forEach(nav => {
                    if (!nav.classList.contains('new')) {
                        nav.classList.remove('active');
                    }
                });
                
                // Add active class to clicked item (except for new button)
                if (!this.classList.contains('new')) {
                    this.classList.add('active');
                }
                
                // Handle different navigation actions
                const page = this.getAttribute('data-page');
                switch(page) {
                    case 'home':
                        console.log('Navigate to Home');
                        break;
                    case 'history':
                        console.log('Navigate to History');
                        break;
                    case 'new':
                        console.log('Create New Content');
                        // Enhanced bounce animation for new button
                        this.style.transform = 'translateX(-50%) translateY(-2px) scale(0.9)';
                        setTimeout(() => {
                            this.style.transform = 'translateX(-50%) translateY(-2px) scale(1)';
                        }, 200);
                        break;
                    case 'discover':
                        console.log('Navigate to Discover');
                        break;
                    case 'profile':
                        console.log('Navigate to Profile');
                        break;
                }
            });
        });
        
        // Enhanced touch feedback for mobile
        navItems.forEach(item => {
            item.addEventListener('touchstart', function(e) {
                e.preventDefault();
                this.style.transform = this.classList.contains('new') ? 
                    'translateX(-50%) translateY(-2px) scale(0.9)' : 'scale(0.9)';
            });
            
            item.addEventListener('touchend', function(e) {
                e.preventDefault();
                setTimeout(() => {
                    this.style.transform = this.classList.contains('new') ? 
                        'translateX(-50%) translateY(-2px) scale(1)' : 'scale(1)';
                }, 100);
            });
        });
        
        // Prevent navigation hiding when interacting with nav items
        bottomNav.addEventListener('touchstart', function(e) {
            e.stopPropagation();
        });
        
        bottomNav.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>