/* ========================================
   RECIPE FINDER - PAGE-SPECIFIC STYLES
   Hero sections, search results, recipe layouts, discover page, and page-specific responsive behavior
   ======================================== */

/* ========================================
   HERO SECTIONS
   ======================================== */
.center-hero-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--space-2) 0;
  overflow-y: auto;
}

/* Mobile-specific styling for center-hero-container - remove ALL spacing */
@media (max-width: 768px) {
  .center-hero-container {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    /* Override any inherited spacing */
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.main-hero-title {
  font-size: 2.2rem;
  font-weight: 600;
  color: #FF6B35;
  letter-spacing: -1px;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .main-hero-title {
    font-size: 2.8rem;
  }
}

@media (max-width: 768px) {
  .main-hero-title {
    font-size: 2rem !important;
  }
}

.hero-section {
  transition: all var(--transition-slow);
}

.hero-section .logo {
  max-width: 250px;
  margin-bottom: 1rem;
}

.hero-section .lead {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: 2rem;
}

/* Hero section visibility on mobile - respect search state */
@media (max-width: 768px) {
  /* Only show hero section when NOT in search mode */
  body:not(.mobile-search-active) #hero-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  body:not(.mobile-search-active) #hero-section .logo,
  body:not(.mobile-search-active) #hero-section .main-hero-title,
  body:not(.mobile-search-active) #hero-section .lead {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  body:not(.mobile-search-active) #hero-section .main-hero-title {
    font-size: 1.8rem !important;
    margin-bottom: 1rem !important;
  }

  body:not(.mobile-search-active) #hero-section .logo {
    max-width: 180px !important;
    margin-bottom: 1rem !important;
  }

  /* Hide hero section when in search mode */
  body.mobile-search-active #hero-section {
    display: none !important;
  }
}

/* ========================================
   SEARCH RESULTS LAYOUTS
   ======================================== */

/* Recipe results container should fill available space */
#recipe-results {
  flex: 1;
  width: 100%;
  min-height: 100%;
  transition: all var(--transition-slow);
}

/* Results section should fill container */
.results-section {
  min-height: 100%;
  width: 100%;
}

/* Results row should fill available space */
#results {
  flex: 1;
  display: flex;
  flex-direction: column;
}

#results-row {
  flex: 1;
  display: flex !important;
}

#results-row .col-12 {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Top Navigation Bar (shown after search) */
.top-nav-bar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e5e7eb;
  padding: var(--space-3) 0;
  z-index: 100;
  transition: transform var(--transition-slow);
}

.top-nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.top-nav-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  margin: 0;
  text-align: center;
}

/* Full-screen results expansion */
.recipe-results-expanded {
  overflow: hidden;
}

.recipe-results-expanded #recipe-results {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #fff;
  overflow-y: auto;
  padding: var(--space-4);
  margin: 0;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced scroll indicators for expanded view */
body.recipe-results-expanded .carousel {
  scrollbar-width: thin;
  scrollbar-color: var(--secondary-color) #f1f1f1;
}

body.recipe-results-expanded .carousel::-webkit-scrollbar {
  height: 8px;
  display: block;
}

body.recipe-results-expanded .carousel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

body.recipe-results-expanded .carousel::-webkit-scrollbar-thumb {
  background: var(--secondary-color);
  border-radius: 4px;
}

/* Recipe grid layouts */
.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

/* Horizontal carousel for featured recipes */
.carousel {
  display: flex;
  overflow-x: auto;
  gap: var(--space-4);
  padding: var(--space-4) 0;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.carousel::-webkit-scrollbar {
  height: 6px;
}

.carousel::-webkit-scrollbar-track {
  background: transparent;
}

.carousel::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

/* Mobile search active state - remove all spacing */
@media (max-width: 768px) {
  body.mobile-search-active .search-container {
    padding: 0 !important;
    margin: 0 !important;
  }

  body.mobile-search-active .search-form-wrapper {
    padding: 0 !important;
    margin: 0 !important;
  }

  body.mobile-search-active .quick-filters {
    margin: 0 !important;
    padding: 0 !important;
  }

  body.mobile-search-active #results {
    padding: 0 !important;
    margin: 0 !important;
    margin-top: 0 !important;
  }

  /* Remove spacing from recipe cards and containers */
  body.mobile-search-active .recipe-card,
  body.mobile-search-active .card,
  body.mobile-search-active .card-body,
  body.mobile-search-active .card-header,
  body.mobile-search-active .card-footer {
    margin: 0 !important;
    padding: 5px !important; /* Minimal padding for readability */
  }

  /* Remove spacing from titles and text elements */
  body.mobile-search-active h1,
  body.mobile-search-active h2,
  body.mobile-search-active h3,
  body.mobile-search-active h4,
  body.mobile-search-active h5,
  body.mobile-search-active h6,
  body.mobile-search-active p,
  body.mobile-search-active .lead {
    margin: 0 !important;
    padding: 2px 0 !important; /* Minimal vertical spacing for readability */
  }

  /* Mobile scroll expansion styles */
  body.recipe-results-expanded {
    overflow: hidden !important;
  }

  body.recipe-results-expanded #recipe-results {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 1000 !important;
    background-color: #fff !important;
    overflow-y: auto !important;
    padding: var(--space-3) !important;
    margin: 0 !important;
  }

  .recipes-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}

/* Landscape orientation fixes */
@media (max-width: 768px) and (orientation: landscape) {
  .center-hero-container {
    min-height: 100vh !important;
  }

  .main-hero-title {
    font-size: 1.5rem !important;
    margin-bottom: var(--space-4) !important;
  }
}

/* ========================================
   DISCOVER PAGE STYLES
   ======================================== */
.discover-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background: var(--neutral-color);
  min-height: 100vh;
}

/* Mobile touch optimizations */
@media (max-width: 768px) {
  /* Improve touch targets */
  .recipe-card,
  .category-chip,
  .randomize-btn,
  .featured-recipe {
    -webkit-tap-highlight-color: rgba(16, 185, 129, 0.2);
    touch-action: manipulation;
  }

  /* Prevent zoom on form inputs */
  .category-chip {
    font-size: 16px; /* Prevent iOS zoom */
  }

  /* Smooth scrolling for categories */
  .categories-strip {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Better visual feedback for touch */
  .recipe-card:active {
    transform: translateY(-1px);
    transition: transform 0.1s ease;
  }

  .category-chip:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .randomize-btn:active {
    transform: translateY(1px);
    transition: transform 0.1s ease;
  }

  .featured-recipe:active {
    transform: scale(0.99);
    transition: transform 0.1s ease;
  }

  /* Ensure proper spacing for mobile navigation */
  body {
    padding-bottom: 0;
  }

  /* Optimize for mobile viewport */
  .discover-container {
    min-height: calc(100vh - 80px);
    overflow-x: hidden;
  }

  /* Improve readability on mobile */
  .featured-recipe-title,
  .recipe-title {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Better loading states */
  .loading {
    text-align: center;
    padding: var(--space-6);
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.5;
  }

  /* Improved error handling */
  .error-message {
    text-align: center;
    padding: var(--space-4);
    margin: 0 var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    line-height: 1.5;
    background: rgba(198, 40, 40, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(198, 40, 40, 0.2);
  }
}

.discover-header {
  text-align: center;
  margin-bottom: 30px;
}

.discover-header h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.featured-recipe {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all var(--transition-slow);
}

.featured-recipe:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.featured-recipe-image {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.featured-recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px 0 0 15px;
}

.featured-recipe-info {
  flex: 1;
  padding: 20px;
}

.featured-recipe-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-dark);
}

.featured-recipe-details {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.featured-recipe-description {
  color: var(--text-light);
  font-size: 0.9rem;
  line-height: 1.4;
}

.randomize-btn {
  background: var(--secondary-color);
  color: var(--text-on-secondary);
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all var(--transition-slow);
  margin-bottom: 30px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.randomize-btn:hover {
  background: var(--secondary-dark);
  transform: translateY(-2px);
}

.randomize-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.categories-strip {
  display: flex;
  gap: 15px;
  margin-bottom: 40px;
  overflow-x: auto;
  padding: 10px 0;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) transparent;
}

.categories-strip::-webkit-scrollbar {
  height: 6px;
}

.categories-strip::-webkit-scrollbar-track {
  background: transparent;
}

.categories-strip::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

.category-chip {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  padding: 8px 20px;
  cursor: pointer;
  transition: all var(--transition-slow);
  white-space: nowrap;
  font-size: 0.9rem;
  color: var(--text-dark);
  text-decoration: none;
}

.category-chip:hover,
.category-chip.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: var(--text-on-primary);
  text-decoration: none;
}

.recipe-info {
  padding: 15px;
}

.recipe-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-dark);
}

.recipe-area {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.recipe-category {
  background: var(--neutral-color);
  color: var(--text-dark);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  display: inline-block;
  border: 1px solid var(--primary-color);
}

.loading {
  text-align: center;
  padding: 40px;
  color: var(--text-light);
  font-size: 1.1rem;
}

.error-message {
  text-align: center;
  padding: 20px;
  color: var(--accent-color);
  background: rgba(198, 40, 40, 0.1);
  border-radius: 10px;
  margin-bottom: 20px;
}

/* Mobile error message styling */
@media (max-width: 768px) {
  .error-message {
    padding: var(--space-4);
    margin: 0 var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

/* Discover page mobile responsive */
@media (max-width: 768px) {
  .discover-container {
    padding: var(--space-4) var(--space-3);
    margin: 0;
    max-width: 100%;
    background: var(--neutral-color);
    min-height: calc(100vh - 80px); /* Account for mobile bottom nav */
    padding-bottom: calc(var(--space-6) + 80px); /* Extra space for mobile nav */
  }

  .discover-header {
    margin-bottom: var(--space-5);
    padding: 0 var(--space-2);
    text-align: center;
  }

  .discover-header h1 {
    font-size: 1.75rem;
    line-height: 1.2;
    margin-bottom: var(--space-2);
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
  }

  .featured-recipe {
    flex-direction: column;
    text-align: left;
    margin-bottom: var(--space-5);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    background: white;
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-slow);
    border: 1px solid rgba(16, 185, 129, 0.1);
  }

  .featured-recipe:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
  }

  .featured-recipe-image {
    width: 100%;
    height: 220px;
    border-radius: 0;
    position: relative;
    overflow: hidden;
  }

  .featured-recipe-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
    transition: transform var(--transition-slow);
  }

  .featured-recipe:hover .featured-recipe-image img {
    transform: scale(1.05);
  }

  .featured-recipe-info {
    padding: var(--space-5);
  }

  .featured-recipe-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-2);
    color: var(--text-dark);
    line-height: 1.3;
  }

  .featured-recipe-details {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: var(--space-3);
    font-weight: var(--font-weight-medium);
  }

  .featured-recipe-description {
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-light);
  }

  .randomize-btn {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
    border-radius: var(--radius-full);
    margin: var(--space-5) auto var(--space-6);
    min-height: 48px; /* Touch-friendly */
    font-weight: var(--font-weight-medium);
    background: var(--secondary-color);
    color: var(--text-on-secondary);
    border: none;
    cursor: pointer;
    transition: all var(--transition-slow);
    display: block;
    width: auto;
    max-width: 280px;
    box-shadow: var(--shadow-md);
  }

  .randomize-btn:hover {
    background: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .randomize-btn:active {
    transform: translateY(0);
  }

  .categories-strip {
    display: flex;
    justify-content: flex-start;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-3) var(--space-4);
    margin-bottom: var(--space-5);
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .categories-strip::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .category-chip {
    padding: var(--space-3) var(--space-4);
    font-size: 16px; /* Prevent iOS zoom */
    border-radius: var(--radius-full);
    min-height: 44px; /* Touch-friendly */
    min-width: 44px; /* Touch-friendly */
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    background: white;
    border: 2px solid #e9ecef;
    color: var(--text-dark);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-slow);
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
  }

  .category-chip:hover,
  .category-chip.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--text-on-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .category-chip:active {
    transform: scale(0.98);
  }

  .recipes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-4);
    margin-bottom: calc(var(--space-6) + 80px); /* Extra space for mobile nav */
    padding: 0 var(--space-1);
  }

  .recipe-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-slow);
    overflow: hidden;
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(16, 185, 129, 0.1);
    min-height: 44px; /* Touch-friendly */
  }

  .recipe-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    text-decoration: none;
    color: inherit;
  }

  .recipe-card:active {
    transform: translateY(-1px);
  }

  .recipe-image {
    height: 200px;
    border-radius: 0;
    overflow: hidden;
    position: relative;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
  }

  .recipe-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
  }

  .recipe-card:hover .recipe-image img {
    transform: scale(1.05);
  }

  .recipe-info {
    padding: var(--space-4);
  }

  .recipe-title {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-2);
    color: var(--text-dark);
    line-height: 1.3;
  }

  .recipe-area {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: var(--space-2);
    font-weight: var(--font-weight-medium);
  }

  .recipe-category {
    background: var(--neutral-color);
    color: var(--primary-color);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    display: inline-block;
    border: 1px solid var(--primary-color);
    font-weight: var(--font-weight-medium);
  }

  .recipe-title {
    font-size: 1rem;
    font-weight: var(--font-weight-semibold);
    line-height: 1.3;
    margin-bottom: var(--space-2);
  }

  .recipe-area {
    font-size: 0.8rem;
    margin-bottom: var(--space-2);
  }

  .recipe-category {
    font-size: 0.75rem;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
  }
}

/* Extra small mobile screens */
@media (max-width: 480px) {
  .discover-container {
    padding: var(--space-3) var(--space-2);
    padding-bottom: calc(var(--space-6) + 80px); /* Extra space for mobile nav */
  }

  .discover-header {
    margin-bottom: var(--space-4);
    padding: 0 var(--space-1);
  }

  .discover-header h1 {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .featured-recipe {
    margin-bottom: var(--space-4);
    border-radius: var(--radius-lg);
  }

  .featured-recipe-image {
    height: 180px;
  }

  .featured-recipe-info {
    padding: var(--space-4);
  }

  .featured-recipe-title {
    font-size: 1.125rem;
    line-height: 1.3;
  }

  .featured-recipe-details {
    font-size: 0.875rem;
    margin-bottom: var(--space-2);
  }

  .featured-recipe-description {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .randomize-btn {
    padding: var(--space-3) var(--space-5);
    font-size: 0.9rem;
    margin: var(--space-4) auto var(--space-5);
    min-height: 44px;
    max-width: 260px;
  }

  .categories-strip {
    gap: var(--space-1);
    padding: var(--space-2) var(--space-2) var(--space-3);
    margin-bottom: var(--space-4);
  }

  .category-chip {
    padding: var(--space-2) var(--space-3);
    font-size: 16px; /* Prevent iOS zoom */
    min-height: 44px; /* Touch-friendly */
    min-width: 44px;
  }

  .recipes-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: var(--space-3);
    margin-bottom: calc(var(--space-5) + 80px);
    padding: 0;
  }

  .recipe-card {
    min-height: 44px;
  }

  .recipe-image {
    height: 160px;
  }

  .recipe-info {
    padding: var(--space-3);
  }

  .recipe-title {
    font-size: 0.95rem;
    line-height: 1.3;
    margin-bottom: var(--space-1);
  }

  .recipe-area {
    font-size: 0.8rem;
    margin-bottom: var(--space-1);
  }

  .recipe-category {
    font-size: 0.75rem;
    padding: 2px var(--space-2);
  }
}

/* Very small screens */
@media (max-width: 375px) {
  .discover-container {
    padding: var(--space-2) var(--space-1);
    padding-bottom: calc(var(--space-5) + 80px);
  }

  .discover-header {
    margin-bottom: var(--space-3);
    padding: 0;
  }

  .discover-header h1 {
    font-size: 1.375rem;
    line-height: 1.2;
  }

  .featured-recipe {
    margin-bottom: var(--space-3);
  }

  .featured-recipe-image {
    height: 160px;
  }

  .featured-recipe-info {
    padding: var(--space-3);
  }

  .featured-recipe-title {
    font-size: 1rem;
  }

  .featured-recipe-details {
    font-size: 0.8rem;
  }

  .featured-recipe-description {
    font-size: 0.75rem;
  }

  .randomize-btn {
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    margin: var(--space-3) auto var(--space-4);
    min-height: 44px;
    max-width: 240px;
  }

  .categories-strip {
    gap: var(--space-1);
    padding: var(--space-2) var(--space-1) var(--space-3);
    margin-bottom: var(--space-3);
  }

  .category-chip {
    font-size: 16px; /* Prevent iOS zoom */
    padding: var(--space-2) var(--space-3);
    min-height: 44px; /* Touch-friendly */
    min-width: 44px;
  }

  .recipes-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
    margin-bottom: calc(var(--space-4) + 80px);
    padding: 0;
  }

  .recipe-image {
    height: 140px;
  }

  .recipe-info {
    padding: var(--space-3);
  }

  .recipe-title {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .recipe-area {
    font-size: 0.75rem;
  }

  .recipe-category {
    font-size: 0.7rem;
    padding: 1px var(--space-2);
  }

  /* Loading and error states */
  .loading {
    padding: var(--space-5);
    font-size: 0.9rem;
  }

  .error-message {
    padding: var(--space-3);
    margin: 0 0 var(--space-3);
    font-size: 0.875rem;
  }
}

/* Loading spinner for randomize button */
.randomize-btn.htmx-request::after {
  content: "🔄";
  animation: spin 1s linear infinite;
  margin-left: 5px;
}

/* ========================================
   RECIPE DETAIL PAGE STYLES
   ======================================== */
.recipe-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-5);
}

.recipe-image-container {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  transition: transform var(--transition-slow);
  box-shadow: var(--shadow-md);
}

.recipe-image-container:hover {
  transform: scale(1.02);
}

.recipe-image-placeholder {
  background: var(--surface);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  height: 300px;
}

.instruction-number {
  font-weight: bold;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  min-width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.recipe-detail-card {
  border: none;
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--space-4);
}

.recipe-detail-card .card-header {
  background: var(--accent-color);
  color: white;
  border-bottom: none;
  padding: var(--space-4);
}

.recipe-detail-card .card-body {
  padding: var(--space-4);
}

.recipe-quick-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
  margin: var(--space-4) 0;
}

.recipe-quick-info .card {
  text-align: center;
  border: none;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.recipe-quick-info .card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.recipe-quick-info .card-body {
  padding: var(--space-4);
}

.recipe-quick-info i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--space-2);
}

.ingredients-list {
  list-style: none;
  padding: 0;
}

.ingredients-list li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
}

.ingredients-list li:last-child {
  border-bottom: none;
}

.ingredients-list li::before {
  content: "🥄";
  margin-right: var(--space-2);
  font-size: 1.2rem;
}

.instructions-list {
  list-style: none;
  padding: 0;
}

.instructions-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background: var(--surface);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.instructions-list li:last-child {
  margin-bottom: 0;
}

/* Recipe detail mobile responsive */
@media (max-width: 768px) {
  .recipe-detail-container {
    padding: var(--space-3);
  }

  .recipe-quick-info {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-2);
  }

  .recipe-quick-info .card-body {
    padding: var(--space-2);
  }

  .recipe-quick-info i {
    font-size: 1.5rem;
  }

  .instruction-number {
    min-width: 25px;
    height: 25px;
    font-size: 0.9rem;
  }
}
