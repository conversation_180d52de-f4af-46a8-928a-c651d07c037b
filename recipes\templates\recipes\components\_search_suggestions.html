<!-- Search Suggestions Component -->
<div id="search-suggestions" class="search-suggestions-container" style="display: none;">
    <div class="suggestions-header">
        <h4 class="suggestions-title">Recent Searches</h4>
        <button class="clear-history-btn" onclick="clearSearchHistory()" title="Clear search history">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>
    
    <div class="suggestions-content">
        <div id="recent-searches" class="recent-searches">
            <!-- Recent searches will be populated by JavaScript -->
        </div>
        
        <div id="search-stats" class="search-stats" style="display: none;">
            <div class="stats-item">
                <span class="stats-label">Total Searches:</span>
                <span class="stats-value" id="total-searches">0</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">This Week:</span>
                <span class="stats-value" id="week-searches">0</span>
            </div>
        </div>
    </div>
</div>

<style>
/* Search Suggestions Styles */
.search-suggestions-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e5e7eb;
    border-top: none;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 10px;
    border-bottom: 1px solid #f3f4f6;
    background: linear-gradient(135deg, #fff3f0, #ffffff);
}

.suggestions-title {
    color: #FF6B35;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.clear-history-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.clear-history-btn:hover {
    background: #f3f4f6;
    color: #FF6B35;
}

.suggestions-content {
    padding: 10px 0;
}

.recent-searches {
    max-height: 300px;
    overflow-y: auto;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f9fafb;
}

.search-suggestion-item:hover {
    background: #fff3f0;
    border-left: 4px solid #FF6B35;
    padding-left: 16px;
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.suggestion-query {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.suggestion-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: #6b7280;
}

.suggestion-type {
    background: #f3f4f6;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.suggestion-type.ingredients { background: #dcfce7; color: #166534; }
.suggestion-type.name { background: #dbeafe; color: #1e40af; }
.suggestion-type.cuisine { background: #fef3c7; color: #92400e; }
.suggestion-type.dietary { background: #f3e8ff; color: #7c3aed; }
.suggestion-type.time { background: #fce7f3; color: #be185d; }

.suggestion-time {
    font-size: 11px;
}

.suggestion-results {
    font-size: 11px;
    color: #059669;
    font-weight: 500;
}

.suggestion-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.search-suggestion-item:hover .suggestion-actions {
    opacity: 1;
}

.suggestion-action-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.suggestion-action-btn:hover {
    background: #f3f4f6;
    color: #FF6B35;
}

.search-stats {
    padding: 15px 20px;
    border-top: 1px solid #f3f4f6;
    background: #f9fafb;
    display: flex;
    justify-content: space-around;
}

.stats-item {
    text-align: center;
}

.stats-label {
    display: block;
    font-size: 11px;
    color: #6b7280;
    margin-bottom: 2px;
}

.stats-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #FF6B35;
}

.no-history-message {
    padding: 30px 20px;
    text-align: center;
    color: #6b7280;
    font-style: italic;
}

.no-history-icon {
    font-size: 24px;
    margin-bottom: 10px;
    opacity: 0.5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .search-suggestions-container {
        max-height: 300px;
        border-radius: 0 0 12px 12px;
    }
    
    .suggestions-header {
        padding: 12px 15px 8px;
    }
    
    .suggestions-title {
        font-size: 14px;
    }
    
    .search-suggestion-item {
        padding: 10px 15px;
    }
    
    .suggestion-query {
        font-size: 13px;
    }
    
    .suggestion-meta {
        gap: 8px;
        font-size: 11px;
    }
    
    .search-stats {
        padding: 12px 15px;
    }
    
    .stats-value {
        font-size: 14px;
    }
}

/* Scrollbar Styling */
.recent-searches::-webkit-scrollbar {
    width: 6px;
}

.recent-searches::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.recent-searches::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.recent-searches::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>

<script>
// Search Suggestions JavaScript Functions
function showSearchSuggestions() {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (suggestionsContainer && window.searchHistoryManager) {
        populateRecentSearches();
        suggestionsContainer.style.display = 'block';
    }
}

function hideSearchSuggestions() {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (suggestionsContainer) {
        setTimeout(() => {
            suggestionsContainer.style.display = 'none';
        }, 200); // Delay to allow for click events
    }
}

function populateRecentSearches() {
    if (!window.searchHistoryManager) return;
    
    const recentSearches = window.searchHistoryManager.getRecentSearches(8);
    const container = document.getElementById('recent-searches');
    
    if (recentSearches.length === 0) {
        container.innerHTML = `
            <div class="no-history-message">
                <div class="no-history-icon">🔍</div>
                <div>No recent searches</div>
                <div style="font-size: 11px; margin-top: 5px;">Your search history will appear here</div>
            </div>
        `;
        return;
    }
    
    container.innerHTML = recentSearches.map(search => `
        <div class="search-suggestion-item" onclick="applySearchSuggestion('${search.query}', '${search.searchType}')">
            <div class="suggestion-main">
                <div class="suggestion-query">${escapeHtml(search.query)}</div>
                <div class="suggestion-meta">
                    <span class="suggestion-type ${search.searchType}">${search.searchType}</span>
                    <span class="suggestion-time">${formatTimeAgo(search.timestamp)}</span>
                    ${search.resultCount > 0 ? `<span class="suggestion-results">${search.resultCount} results</span>` : ''}
                </div>
            </div>
            <div class="suggestion-actions">
                <button class="suggestion-action-btn" onclick="event.stopPropagation(); removeSearchSuggestion('${search.id}')" title="Remove">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>
    `).join('');
    
    // Update stats
    updateSearchStats();
}

function applySearchSuggestion(query, searchType) {
    // Fill the search form with the suggestion
    const searchInput = document.querySelector('input[name="search_query"]');
    const searchTypeInput = document.querySelector(`input[name="search_type"][value="${searchType}"]`);
    
    if (searchInput) {
        searchInput.value = query;
    }
    
    if (searchTypeInput) {
        searchTypeInput.checked = true;
        // Trigger Alpine.js update if available
        const alpineData = searchInput.closest('[x-data]');
        if (alpineData && alpineData._x_dataStack) {
            alpineData._x_dataStack[0].filter = searchType;
        }
    }
    
    hideSearchSuggestions();
    
    // Optionally trigger the search automatically
    const searchForm = document.querySelector('#main-search-box form');
    if (searchForm) {
        searchForm.dispatchEvent(new Event('submit'));
    }
}

function removeSearchSuggestion(id) {
    if (window.searchHistoryManager) {
        window.searchHistoryManager.removeSearchEntry(id);
        populateRecentSearches();
    }
}

function clearSearchHistory() {
    if (window.searchHistoryManager && confirm('Are you sure you want to clear your search history?')) {
        window.searchHistoryManager.clearHistory();
        populateRecentSearches();
    }
}

function updateSearchStats() {
    if (!window.searchHistoryManager) return;
    
    const stats = window.searchHistoryManager.getSearchStats();
    const totalElement = document.getElementById('total-searches');
    const weekElement = document.getElementById('week-searches');
    
    if (totalElement) totalElement.textContent = stats.totalSearches;
    if (weekElement) weekElement.textContent = stats.recentActivity.length;
    
    // Show stats if there are searches
    const statsContainer = document.getElementById('search-stats');
    if (statsContainer && stats.totalSearches > 0) {
        statsContainer.style.display = 'flex';
    }
}

function formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
